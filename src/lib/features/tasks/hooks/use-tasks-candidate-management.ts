import { useCallback, useEffect, useMemo, useState } from 'react'
import { useMutation } from 'urql'
import DeleteTaskMutation from '../graphql/delete-task-mutation'
import QueryTasksList from '../graphql/query-tasks-list'
import UpdateTaskMutation from '../graphql/update-task-mutation'
import { ITasksManagementFilter } from '../types'
import { trimObjectProps } from '../utilities/common'
import { useInfinityGraphPage } from '../../jobs/hooks/use-infinity-graph-page'
import configuration from '~/configuration'
import { IRouterWithID } from '~/core/@types/global'
import { FILTER_BY_DEFAULT } from '../../agency/companies/utilities/company-detail-enum'

const useTasksCandidateManagement = ({
  applicantId,
  profileId
}: {
  applicantId?: IRouterWithID
  profileId?: IRouterWithID
}) => {
  const [filterValue, onChangeFilter] = useState<
    ITasksManagementFilter | undefined
  >()

  const taskPaging = useInfinityGraphPage({
    queryDocumentNote: QueryTasksList,
    getVariable: useCallback(
      (page) => {
        const { filterBy } = filterValue || {}
        return trimObjectProps({
          limit: configuration.defaultPageSize,
          page,
          filterBy: filterBy || FILTER_BY_DEFAULT,
          ...(applicantId ? { applicantId: Number(applicantId) } : {}),
          ...(profileId && !applicantId ? { profileId: Number(profileId) } : {})
        })
      },
      [filterValue, applicantId, profileId]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.tasksList?.metadata?.totalCount,
      pageLength: groups?.[0]?.tasksList?.collection?.length
    }),
    queryKey: ['tasks-candidate-management-list-assigned']
  })

  useEffect(() => {
    taskPaging.refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue])

  const [{ fetching: deletingTask }, deleteTask] =
    useMutation(DeleteTaskMutation)
  const [{ fetching: updatingTask }, updateTask] =
    useMutation(UpdateTaskMutation)

  return {
    taskPaging,
    filterControl: useMemo(
      () => ({ value: filterValue, onChange: onChangeFilter }),
      [filterValue]
    ),
    action: {
      taskDeleteAction: {
        deleteTask: (args: { id: number; profileId?: number }) =>
          deleteTask(args),
        deletingTask
      },
      updateTaskAction: {
        updateTask: (args: {
          id: number
          applicantId?: IRouterWithID
          title?: string
          profileId?: number | null
          companyId?: number | null
          dueDate?: string
          assigneeIds?: Array<number>
        }) => updateTask(args),
        updatingTask,
        updateTaskStatus: (args: {
          id: number
          applicantId?: IRouterWithID
          status?: string
          title?: string
          assigneeIds?: Array<number>
        }) => updateTask(args)
      }
    }
  }
}

export default useTasksCandidateManagement
