import { useRouter } from 'next/router'
import { useCallback, useState } from 'react'
import { useMutation } from 'urql'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import useToastStore from '~/lib/store/toast'
import { ApproveRejectPublicRequisition } from '../graphql/approve-reject-public-requisition-mutation'
import QueryPublicRequisitionDetail from '../graphql/query-public-requisition-detail'
import { PublicRequisitionDetailType } from '../types/requisition-public'

const usePublicRequisitionDetailHook = () => {
  const { clientGraphQL } = useContextGraphQL()
  const router = useRouter()
  const { setToast } = useToastStore()
  const [requisitionDetail, setRequisitionDetail] =
    useState<PublicRequisitionDetailType>()

  const scrollToTopPage = useCallback(() => {
    setTimeout(() => {
      window.scrollTo(0, 0)
    }, 100)
  }, [])

  const fetchRequisitionDetail = useCallback(
    ({ uuid, userToken }: { uuid: string; userToken: string }) => {
      return clientGraphQL
        .query(QueryPublicRequisitionDetail, {
          uuid,
          userToken
        })
        .toPromise()
        .then(
          (result: {
            error: { graphQLErrors: Array<object> }
            data: {
              publicRequisitionsShow: PublicRequisitionDetailType
            }
          }) => {
            if (result.error) {
              return catchErrorFromGraphQL({
                error: result.error,
                setToast,
                router
              })
            }

            return setRequisitionDetail(result.data.publicRequisitionsShow)
          }
        )
    },
    []
  )

  const [{ fetching: updatingStatus }, approveRejectRequisition] = useMutation(
    ApproveRejectPublicRequisition
  )

  const updateRequisitionStatus = useCallback(
    (data: {
      uuid: string
      userToken: string
      status?: string
      rejectReason?: string
    }) => {
      return approveRejectRequisition(data).then((result) => {
        if (result.error) {
          if (result.error.response.status === 422) {
            fetchRequisitionDetail({
              uuid: data.uuid,
              userToken: data.userToken
            }).then(() => {
              scrollToTopPage()
            })
          }
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            router
          })
        }

        return fetchRequisitionDetail({
          uuid: data.uuid,
          userToken: data.userToken
        }).then(() => {
          scrollToTopPage()
        })
      })
    },
    []
  )

  return {
    requisitionDetail,
    fetchRequisitionDetail,
    updatingStatus,
    updateRequisitionStatus
  }
}

export default usePublicRequisitionDetailHook
