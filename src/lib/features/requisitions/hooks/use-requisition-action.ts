import { useMutation } from 'urql'
import { DeleteRequisitionMutation } from '../graphql/delete-requisition-mutation'
import { RemindRequisitionMutation } from '../graphql/remind-requisition-mutation'
import { UpdateRequisitionMutation } from '../graphql/update-requisition-mutation'

export const useRequisitionAction = () => {
  const [{ fetching: updatingRequisition }, updateRequisition] = useMutation(
    UpdateRequisitionMutation
  )

  const [{ fetching: remindingRequisition }, remindRequisition] = useMutation(
    RemindRequisitionMutation
  )

  const [{ fetching: deletingRequisition }, deleteRequisition] = useMutation(
    DeleteRequisitionMutation
  )

  return {
    update: {
      updateRequisition,
      updatingRequisition
    },
    remind: {
      remindingRequisition,
      remindRequisition
    },
    deleteAction: {
      deleteRequisition,
      deletingRequisition
    }
  }
}
