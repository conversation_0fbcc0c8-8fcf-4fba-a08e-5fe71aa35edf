import { gql } from 'urql'

const QueryChubMemberSettingList = gql`
  query ($limit: Int!, $page: Int!, $search: String) {
    tenantChubMemberSettingList(limit: $limit, page: $page, search: $search) {
      collection {
        id
        email
        fullName
        avatarVariants
        defaultColour
        confirmed
        roles {
          id
          name
          code
        }
        ownTenant
        departments {
          id
          name
          subordinates {
            id
            name
          }
          parent {
            id
            name
          }
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryChubMemberSettingList
