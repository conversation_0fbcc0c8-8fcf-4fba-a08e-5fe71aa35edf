import { gql } from 'urql'

const QueryPositionList = gql<
  {
    tenantPositionsList: {
      collection: { id: number; name: string; similar: [string] }[]
      metadata: { totalCount: number }
    }
  },
  {}
>`
  query (
    $limit: Int!
    $page: Int!
    $search: String
    $statuses: [PositionStatus!]
  ) {
    tenantPositionsList(
      limit: $limit
      page: $page
      search: $search
      statuses: $statuses
    ) {
      collection {
        id
        name
        description
        skills
        status
        linkedToProfiles
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryPositionList
