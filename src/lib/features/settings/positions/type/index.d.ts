export type IPositionForm = {
  id?: number
  status?: string
  name?: string
  description?: string
  skills?: Array<string>
  linkedToProfiles?: boolean
}

export type IPositionDetails = {
  id?: string
  status?: string
  name?: string
  description?: string
  skills?: Array<IPositionSkillPage>
  updatedAt?: string
  createdAt?: string
  linkedToProfiles?: boolean
  updatedBy?: {
    fullName: string
    defaultColour: string
    avatarVariants: ILogoAndAvatarVariants
  }
  createdBy?: {
    fullName: string
    defaultColour: string
    avatarVariants: ILogoAndAvatarVariants
  }
}

export interface IPositionSkillPage {
  supportingObj?: {
    name: string
  }
  value: string
}

export interface IPositionPage {
  data: Array<IPositionForm>
  metadata: {
    totalRowCount: number
    currentPage: number
  }
}

export type SkillsType = {
  name: string
  id: string
  similar: Array<string>
}

interface IPositionForm {
  [key: string]: { message?: string }
}

export interface IPromisePositionSearchOption {
  search?: string
  page?: number
  limit?: number
  statuses?: ISelectOption[]
}

export interface IPositionAIWriterInput {
  title: string
  description: string
  toneType: string
  skills?: string[]
  language: string
}

export interface IPositionAIWriter {
  toneType?: string
  language?: string
}

export interface QueryPositionProps {
  search?: string | number
  page?: number
  key?: number
  limit?: number
  statuses?: ISelectOption[]
}
