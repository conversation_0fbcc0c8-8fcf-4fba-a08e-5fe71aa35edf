import { trimFirstContentBreakLine } from '~/core/utilities/common'
import { IPositionForm } from '../type'
import { POSITION_STATUS_ENUM } from '../utilities/enum'

export const mappingPositionAddGraphQL = (data: IPositionForm) => {
  return {
    name: data.name,
    status: data.status || POSITION_STATUS_ENUM.active,
    description: trimFirstContentBreakLine(data.description),
    skills: (data.skills || []).length > 0 ? data.skills || [] : undefined
  }
}

export const mappingPositionEditGraphQL = (data: IPositionForm) => {
  return {
    id: data.id,
    name: data.name,
    status: data.status || POSITION_STATUS_ENUM.active,
    description: trimFirstContentBreakLine(data.description),
    skills: (data.skills || []).length > 0 ? data.skills || [] : undefined
  }
}
