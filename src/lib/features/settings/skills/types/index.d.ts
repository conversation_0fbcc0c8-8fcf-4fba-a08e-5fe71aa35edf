export interface ISkillForm {
  id?: string
  name: string
  group?: ISelectOption
  similar?: ISelectOption[]
  description?: string
}

export interface IAddSkillForm {
  name: string
  group: ISelectOption
}

export interface ISkill {
  id?: string
  key?: string
  parentId?: string
  parentName?: string
  name?: string
  enabled?: boolean
  groupTotalCount?: number
  groupProfilesCount?: number
  groupJobsCount?: number
  profilesCount?: number
  jobsCount?: number
  updatedAt?: string
  description?: string
  similar?: string[]
  equivalentSkills?: string[]
  subordinates?: Array<{
    id?: string
    parentId?: string
    parentName?: string
    name?: string
    description?: string
    similar?: string[]
    enabled?: boolean
    equivalentSkills?: string[]
    isFEDeleted?: boolean // FE used
  }>
  group?: ISelectOption
  parent?: {
    id: number | string
    name: string
  }

  isFEDeleted?: boolean // FE used
}
export interface ISkillPromiseSearchOption {
  search: string
  page?: number
  limit?: number
  parentId?: number
}

export interface ISkillReportManagementFilter {
  id?: number
  dateRange?: {
    from?: string | Date
    to?: string | Date
  }
}

export interface ISkillReportOvertime {
  from_date: string
  group: string
  groupped_date: string
  summary: { profiles_count: number; jobs_count: number }
  to_date: string
}

export type ISkillReportDetailProfile = {
  id: number
  fullName: string
  avatarVariants?: ILogoAndAvatarVariants
  sourcedDescription: string
  owner?: {
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  job: {
    id: number
    title: string
    status: string
    statusDescription?: string
    owner?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
  rejectedReasonLabel: string
  jobStage: IJobStage
  createdAt: string
  hiredDate: string
  status: string
  timeToHire: number
  referral?: {
    user: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
    createdAt: string
  }
  applicants?: Array<{
    id?: number
    job?: {
      id?: number
      title?: string
      status: string
      statusDescription: string
      currentUserAccessible: boolean
    }
    jobStage?: { stageLabel?: string; stageTypeId?: number }
    flagNew: boolean
    rejectedReasonLabel: string
    status: string
  }>
}

export interface ISkillPage {
  data: Array<ISkill>
  meta: { totalRowCount: number }
}

export interface QuerySkillGroupProps {
  search?: string
  page?: number
  key?: number
  limit?: number
  parentIds?: ISelectOption[]
  excludeParents?: boolean
}
