import { IUserInformation } from '~/core/@types/global'
import { monthFormatDate, yearFormatDate } from '~/core/utilities/format-date'
import {
  EducationsType,
  LanguagesType,
  WorkExperiencesType
} from '~/lib/features/candidates/types'
import { formatInitialValueCustomField } from '../../../profile-fields/mapping/custom-field-mapping'
import {
  IProfileTemplate,
  IProfileTemplateMetrics
} from '../../../profile-templates/types'
import { ICertificateParamType, IResume } from '../types'

export const mappingPrepareFromQuery = (resumeData: IResume) => {
  return {
    ...resumeData,
    cvTemplateSections: resumeData?.cvTemplateSections,
    permittedFields: {
      ...(resumeData?.permittedFields || []),
      avatar: resumeData?.permittedFields?.avatar,
      certificates: resumeData?.permittedFields?.certificates
        ? {
            ...resumeData?.permittedFields?.certificates,
            value: (
              resumeData?.permittedFields?.certificates?.value || []
            )?.map((item, index) => ({
              ...item,
              position: index
            }))
          }
        : undefined,
      educations: resumeData?.permittedFields?.educations
        ? {
            ...resumeData?.permittedFields?.educations,
            value: (resumeData?.permittedFields?.educations?.value || [])?.map(
              (item, index) => ({
                ...(item as EducationsType),
                position: index
              })
            )
          }
        : undefined,
      references: resumeData?.permittedFields?.references
        ? {
            ...resumeData?.permittedFields?.references,
            value: (resumeData?.permittedFields?.references?.value || [])?.map(
              (item, index) => ({
                ...item,
                index
              })
            )
          }
        : undefined,
      workExperiences: resumeData?.permittedFields?.workExperiences
        ? {
            ...resumeData?.permittedFields?.workExperiences,
            value: (
              resumeData?.permittedFields?.workExperiences?.value || []
            )?.map((item, index) => ({
              ...(item as WorkExperiencesType),
              position: index
            }))
          }
        : undefined,
      languages: resumeData?.permittedFields?.languages
        ? {
            ...resumeData?.permittedFields?.languages,
            value: resumeData?.permittedFields?.languages?.value || []
          }
        : undefined,
      birthday: resumeData?.permittedFields?.birthday
        ? {
            ...resumeData?.permittedFields?.birthday,
            value: {
              year: resumeData?.permittedFields?.birthday?.value?.birth_year,
              month: resumeData?.permittedFields?.birthday?.value?.birth_month,
              date: resumeData?.permittedFields?.birthday?.value?.birth_date
            }
          }
        : undefined
    }
  }
}

export const mappingReorderListIndex = (list: any[]) => {
  return list.map((item, index) => {
    return {
      ...item,
      index
    }
  })
}

export const mappingReorderListPosition = (list: any[]) => {
  return list.map((item, index) => {
    return {
      ...item,
      position: index
    }
  })
}

export const formatSelectOption = (value: unknown) => {
  return value
    ? [
        {
          value: String(value),
          supportingObj: {
            name: String(value)
          }
        }
      ]
    : []
}

export const mappingCustomFieldGroup = (
  resumeData: IResume,
  user: IUserInformation
) => {
  const permittedFields = resumeData?.permittedFields
  return {
    ...resumeData,
    publicId: permittedFields?.publicId?.value,
    fullName: permittedFields?.fullName?.value,
    email: permittedFields?.email?.value,
    phoneNumber: permittedFields?.phoneNumber?.value,
    headline: permittedFields?.headline?.value,
    totalYearsOfExp: permittedFields?.totalYearsOfExp?.value,
    typeOfExpectedSalary: permittedFields?.typeOfExpectedSalary?.value,
    typeOfCurrentSalary: permittedFields?.typeOfCurrentSalary?.value,
    currentSalary:
      Number(permittedFields?.currentSalary?.value) > 0
        ? Number(permittedFields?.currentSalary?.value)
        : '',
    currentSalaryInUsd: permittedFields?.currentSalaryInUsd?.value,
    currentSalaryCurrency:
      permittedFields?.currentSalaryCurrency?.value ||
      user?.currentTenant?.currency,
    expectedSalary:
      Number(permittedFields?.expectedSalary?.value) > 0
        ? Number(permittedFields?.expectedSalary?.value)
        : '',
    expectedSalaryCurrency:
      permittedFields?.expectedSalaryCurrency?.value ||
      user?.currentTenant?.currency,
    expectedSalaryInUsd: permittedFields?.expectedSalaryInUsd?.value,
    birthday: permittedFields?.birthday?.value, // this had been formatted once before this step
    profileLevel: permittedFields?.profileLevel?.value,
    nationality: permittedFields?.nationality?.value,
    willingToRelocate: permittedFields?.willingToRelocate?.value,
    noticeToPeriodDays: permittedFields?.noticeToPeriodDays?.value,
    skills: permittedFields?.skills?.value,
    departments: permittedFields?.departments?.value?.departments,
    profileTalentPoolIds: (permittedFields?.talentPools?.value || [])?.map(
      (item: { name: string }) => Number(item.name)
    ),
    editorLinks: Object.entries(permittedFields?.links?.value || {}).reduce(
      (acc, [key, value]) => {
        // @ts-expect-error
        acc[key] = value
        return acc
      },
      {}
    ),
    editorLanguages: (permittedFields?.languages?.value || []).map((item) =>
      mappingLanguageForm(item)
    ),
    languages: permittedFields?.languages?.value,
    customFields: formatInitialValueCustomField(resumeData.customFields),
    locationWithStateID: permittedFields?.location?.value
      ? {
          id: resumeData?.countryStateId,
          value: permittedFields.location.value,
          supportingObj: {
            name: permittedFields.location.value
          }
        }
      : undefined
  }
}

export const mappingPersonalSummary = (resumeData: IResume) => {
  return {
    summary: resumeData?.permittedFields?.summary?.value || ''
  }
}

export const mappingCertificateForm = (item: ICertificateParamType) => {
  return {
    ...item,
    issueMonth: String(item.issueMonth || ''),
    issueYear: String(item.issueYear || '')
  }
}

export const mappingEducationForm = (item: EducationsType) => {
  return {
    ...item,
    from: {
      // @ts-expect-error
      month: item?.from?.month
        ? // @ts-expect-error
          monthFormatDate(new Date(item.from.month))
        : typeof item.from === 'string'
        ? monthFormatDate(new Date(item.from))
        : '',
      // @ts-expect-error
      year: item?.from?.year
        ? // @ts-expect-error
          yearFormatDate(new Date(item.from?.year))
        : typeof item.from === 'string'
        ? yearFormatDate(new Date(item.from))
        : ''
    },
    to: {
      // @ts-expect-error
      month: item?.to?.month
        ? // @ts-expect-error
          monthFormatDate(new Date(item.to.month))
        : typeof item.to === 'string'
        ? monthFormatDate(new Date(item.to))
        : '',
      // @ts-expect-error
      year: item?.to?.year
        ? // @ts-expect-error
          yearFormatDate(new Date(item.to?.year))
        : typeof item.to === 'string'
        ? yearFormatDate(new Date(item.to))
        : ''
    }
  }
}

export const mappingWorkExpForm = (item: WorkExperiencesType) => {
  return {
    ...item,
    fromMonth: item.fromMonth ? item.fromMonth.toString() : undefined,
    toMonth: item.toMonth ? item.toMonth.toString() : undefined,
    fromYear: item.fromYear ? item.fromYear.toString() : undefined,
    toYear: item.toYear ? item.toYear.toString() : undefined,
    // @ts-expect-error
    location: item?.location?.id
      ? {
          // @ts-expect-error
          id: item?.location?.id,
          // @ts-expect-error
          value: item?.location?.value,
          supportingObj: {
            // @ts-expect-error
            name: item?.location?.value
          }
        }
      : item?.location
      ? {
          id: item?.countryStateId,
          value: item?.location,
          supportingObj: {
            name: item?.location
          }
        }
      : undefined,
    currentWorking: item?.currentWorking || false
  }
}

export const mappingLanguageForm = (item: LanguagesType) => {
  return {
    index: item.index,
    language: item.language
      ? {
          value: item.language || '',
          supportingObj: {
            name: item.languageDescription || ''
          }
        }
      : undefined,
    proficiency: item.proficiencyDescription
      ? {
          value: item.proficiency || '',
          supportingObj: {
            name: item.proficiencyDescription || ''
          }
        }
      : undefined
  }
}

export const mappingCvTemplateSections = (
  cvTemplatesShow: IProfileTemplate,
  isEmployee: boolean,
  isCompanyKind: boolean
) => {
  return {
    isDefault: false,
    templateName: cvTemplatesShow.name,
    templateNameEnabling: cvTemplatesShow.templateNameEnabling,
    templateGuideline: cvTemplatesShow.guideline,
    dateEnabling: cvTemplatesShow.dateEnabling,
    profileIdEnabling: cvTemplatesShow.profileIdEnabling,
    fullnameEnabling: cvTemplatesShow.fullnameEnabling,
    emailEnabling: cvTemplatesShow.emailEnabling,
    phoneNumberEnabling: cvTemplatesShow.phoneNumberEnabling,
    avatarEnabling: cvTemplatesShow.avatarEnabling,
    logoEnabling: cvTemplatesShow.logoEnabling,
    watermarkEnabling: cvTemplatesShow.watermarkEnabling,
    templateStyle: cvTemplatesShow.templateStyle,
    cvTemplateSections: (cvTemplatesShow.cvTemplateSections || [])
      ?.sort(
        (a: IProfileTemplateMetrics, b: IProfileTemplateMetrics) =>
          Number(a.index) - Number(b.index)
      )
      ?.map((item) => {
        return {
          key: item.setting?.field,
          name: item.name,
          customRelatedFields: item.customRelatedFields || {},
          cvTemplateCustomFields: (item.cvTemplateCustomFields || [])
            ?.sort(
              (a: IProfileTemplateMetrics, b: IProfileTemplateMetrics) =>
                Number(a.index) - Number(b.index)
            )
            ?.map((subItem) => ({
              key: subItem.setting?.field,
              name: subItem.setting?.name,
              isCustom: !!subItem.customFieldSetting?.id,
              customSettingId: subItem.customFieldSetting?.id,
              fieldKind: subItem.customFieldSetting?.fieldKind,
              fieldName: subItem.customFieldSetting?.fieldName,
              selectOptions: subItem.customFieldSetting?.selectOptions,
              visibility: subItem.customFieldSetting?.visibility,
              visibleToEmployeeProfile: isCompanyKind
                ? true
                : !isEmployee
                ? true
                : subItem.customFieldSetting?.visibleToEmployeeProfile,
              customRelatedFields: subItem.customRelatedFields || {}
            }))
        }
      })
  }
}
