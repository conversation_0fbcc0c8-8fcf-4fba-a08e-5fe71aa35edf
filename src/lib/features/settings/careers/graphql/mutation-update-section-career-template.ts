const MutationUpdateSectionCareerPageTemplate = `
  mutation ($id: Int!, $fieldId: Int, $hidden: Boolean, $index: Int, $values: JSON, $images: [File!], $section: JSON) {
    careerPageSectionUpdate(
      input: {
        id: $id
        fieldId: $fieldId
        hidden: $hidden
        index: $index
        values: $values
        images: $images
        section: $section
      }
    ) {
        section {
          id
          name
          key
          index
          hidden
          hiddenChangable
          sectionableType
          fields {
            id
            index
            key
            name
            fieldKind
            values
            required
            images {
              id
              file
            }
            sections {
              id
              name
              key
              index
              locked
              hidden
              hiddenChangable
              sectionableType
              fields {
                id
                index
                key
                name
                required
                fieldKind
                required
                values
                images {
                    id
                    file
                }
              }
            }
          }
        }
      }
    }
  }
`

export default MutationUpdateSectionCareerPageTemplate
