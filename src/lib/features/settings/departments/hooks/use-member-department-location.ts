import { useTranslation } from 'react-i18next'
import configuration from '~/configuration'
import { IPromiseSearchOption } from '~/core/@types/global'
import { DEFAULT_MAX_PAGE_SIZE } from '~/core/constants/enum'
import useContextGraphQL, {
  IResponseContextResult
} from '~/core/middleware/use-context-graphQL'
import { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import QueryTenantDepartment from '~/lib/features/settings/departments/graphql/query-tenant-department'
import { IDepartment } from '~/lib/features/settings/departments/types'
import QueryTenantLocation from '~/lib/features/settings/locations/graphql/query-tenant-location'
import { ILocation } from '~/lib/features/settings/locations/types'

const useMemberDepartmentLocationJob = () => {
  const { t } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()

  const promiseLocationOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>((resolve) => {
      clientGraphQL
        .query(QueryTenantLocation, params)
        .toPromise()
        .then((result: IResponseContextResult<ILocation>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantLocationsList } = result.data
          const collection = tenantLocationsList?.collection || []
          const metadata = tenantLocationsList?.metadata || {}

          const cloneData = collection.map((item: ILocation) => {
            return {
              value: item.id,
              supportingObj: {
                name: item.name,
                badge: item.headquarter
                  ? `${t('settings:teamMembers:headQuarter')}`
                  : undefined,
                description: [item.address, item.city, item.state]
                  .filter((item) => item)
                  .join(', ')
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  const promiseDepartmentMultiLevelOptions = (
    params = {} as IPromiseSearchOption
  ) =>
    new Promise<any>((resolve) => {
      clientGraphQL
        .query(QueryTenantDepartment, {
          ...params,
          limit: DEFAULT_MAX_PAGE_SIZE
        })
        .toPromise()
        .then((result: IResponseContextResult<IDepartment>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantDepartmentsList } = result.data
          const collection = tenantDepartmentsList?.collection || []
          const metadata = tenantDepartmentsList?.metadata || {}

          const cloneData = collection.map((item: IDepartment) => {
            return {
              id: item.id,
              name: item.name,
              openJobsCount: item.openJobsCount,
              parentId: item.parentId,
              subordinates: item.subordinates
            }
          })

          const newCloneData = [] as Array<ISelectOption>
          cloneData.forEach((item) => {
            newCloneData.push({
              value: String(item.id),
              parentId: undefined,
              supportingObj: {
                name: item.name || ''
              },
              //@ts-ignore
              subordinates: item.subordinates
            })

            if (item.subordinates?.length) {
              item.subordinates.forEach((sub) => {
                newCloneData.push({
                  value: String(sub.id),
                  parentId: String(item.id),
                  supportingObj: {
                    name: sub.name || ''
                  }
                })
              })
            }
          })
          return resolve({ metadata, collection: newCloneData })
        })
    })
  return { promiseLocationOptions, promiseDepartmentMultiLevelOptions }
}
export default useMemberDepartmentLocationJob
