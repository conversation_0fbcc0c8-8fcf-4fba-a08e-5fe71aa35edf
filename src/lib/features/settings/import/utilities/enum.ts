import { TFunction } from 'i18next'
import { IColorBadgeType } from '~/core/ui/Badge'

export const PROGRESS_IMPORT = (t: TFunction) => {
  return [
    {
      label: `${t('settings:import:upload_files')}`,
      step: 0
    },
    {
      label: `${t('settings:import:mapping_data')}`,
      step: 1
    },
    {
      label: `${t('settings:import:import')}`,
      step: 2
    }
  ]
}

export const ENUM_IMPORT_FILE_STATUS = {
  in_progress: 'in_progress',
  failed: 'failed',
  completed: 'completed',
  partial: 'partial'
}

export const IMPORT_FILE_STATUS_COLOR: { [key: string]: IColorBadgeType } = {
  completed: 'green',
  in_progress: 'yellow',
  failed: 'red'
}

export const ENUMS_IMPORT_TYPE = {
  jobs: 'jobs',
  candidate: 'candidate',
  course: 'course'
}

export const IMPORT_JOBS = {
  value: 'jobs',
  text: 'Jobs',
  description: '',
  key: false,
  templateUrl: {
    direct: 'https://link.hireforce.io/import-job-direct',
    agency: 'https://link.hireforce.io/import-job-agency'
  }
}

export const IMPORT_CANDIDATES = {
  value: 'candidate',
  text: 'Candidate',
  description: '',
  key: false,
  templateUrl: {
    direct: 'https://link.hireforce.io/import-candidate',
    agency: 'https://link.hireforce.io/import-candidate'
  }
}

export const IMPORT_COURSES = {
  value: 'course',
  text: 'Course',
  description: '',
  key: false,
  templateUrl: {
    direct: 'https://link.hireforce.io/import-course',
    agency: 'https://link.hireforce.io/import-course'
  }
}

export const IMPORT_TYPE_URL = {
  jobs: 'job',
  candidate: 'profile',
  course: 'tenant_course'
}
