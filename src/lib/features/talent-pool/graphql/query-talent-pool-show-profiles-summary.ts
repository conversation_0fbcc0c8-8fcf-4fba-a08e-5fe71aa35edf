import { gql } from 'urql'
import {
  ITalentPoolReportManagementFilter,
  ITalentPoolType
} from '../types/talent-pool-type'

const QueryTalentPoolShowProfilesSummaryDetails = gql<
  {
    talentPoolsShowProfilesSummaryDetails: {
      data: Array<ITalentPoolType>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  ITalentPoolReportManagementFilter
>`
  query (
    $id: Int!
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $page: Int
    $limit: Int
  ) {
    talentPoolsShowProfilesSummaryDetails(
      id: $id
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      page: $page
      limit: $limit
    ) {
      collection {
        id
        fullName
        email
        avatarVariants
        phoneNumber
        createdAt
        updatedAt
        links
        tags {
          name
        }
        permittedFields
      }
      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryTalentPoolShowProfilesSummaryDetails
