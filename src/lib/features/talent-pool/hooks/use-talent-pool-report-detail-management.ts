import { endOfDay, startOfDay } from 'date-fns'
import { AnyVariables, DocumentInput } from 'urql'
import {
  addTzToDate,
  changeTimezone
} from '~/lib/features/calendar/utilities/helper-schedule-interview'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import useBoundStore from '~/lib/store'
import { ITalentPoolReportManagementFilter } from '../types/talent-pool-type'

export const useTalentPoolReportDetailManagement = ({
  queryReportDetail,
  filterControl,
  typeModal,
  sortDirection
}: {
  sortDirection?: { direction?: string; field?: string }
  queryReportDetail?: DocumentInput<any, AnyVariables>
  typeModal?: string
  filterControl?: {
    value?: ITalentPoolReportManagementFilter | undefined
    onChange?: ((value?: ITalentPoolReportManagementFilter) => void) | undefined
  }
}) => {
  const user = useBoundStore((state) => state.user)

  const {
    data,
    status,
    error,
    isFetching,
    fetchPagination,
    forceChangeCurrentPage,
    globalFilter,
    setGlobalFilter
  } = usePaginationGraphPage({
    queryDocumentNode: queryReportDetail as any,
    queryKey: 'use-talent-pool-detail-management',
    filter: {
      id: filterControl?.value?.id,
      ...(filterControl?.value?.dateRange?.from
        ? {
            fromDatetime: addTzToDate(
              String(
                startOfDay(new Date(filterControl?.value?.dateRange?.from))
              ),
              String(user.timezone)
            )
          }
        : {
            fromDatetime: addTzToDate(
              String(
                startOfDay(
                  changeTimezone({
                    date: new Date(
                      new Date().setDate(new Date().getDate() - 365)
                    ),
                    timezone: user.timezone
                  })
                )
              ),
              String(user.timezone)
            )
          }),
      ...(filterControl?.value?.dateRange?.to
        ? {
            toDatetime: addTzToDate(
              String(endOfDay(new Date(filterControl?.value?.dateRange?.to))),
              String(user.timezone)
            )
          }
        : {
            toDatetime: addTzToDate(
              String(
                endOfDay(
                  changeTimezone({
                    date: new Date(),
                    timezone: user.timezone
                  })
                )
              ),
              String(user.timezone)
            )
          }),
      modelKeY: typeModal || '',
      sortDirection: sortDirection
        ? {
            direction: sortDirection?.direction,
            field: sortDirection?.field
          }
        : undefined
    }
  })

  return {
    data,
    status,
    error,
    isFetching,
    globalFilter,
    setGlobalFilter,
    fetchPagination,
    forceChangeCurrentPage
  }
}
