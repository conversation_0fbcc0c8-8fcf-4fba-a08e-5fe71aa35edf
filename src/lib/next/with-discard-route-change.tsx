import { ReactElement, useEffect } from 'react'

import { useDiscardRouteChange } from '../hooks/use-discard-route-change'

const withDiscardRouteChange = <
  T extends { anyChangesForm?: boolean; setAnyChangesFrom?: () => void }
>(
  Component: (props: T) => ReactElement
) => {
  const WithDiscardRouteChange = <A extends T>(props: A) => {
    const { anyChangesForm, setAnyChangesFrom } = useDiscardRouteChange()

    const beforeUnLoad = (e: BeforeUnloadEvent) => {
      if (anyChangesForm) {
        e.preventDefault()
        e.stopPropagation()
        e.returnValue = ''
      }
    }

    useEffect(() => {
      window.addEventListener('beforeunload', beforeUnLoad)

      return () => {
        window.removeEventListener('beforeunload', beforeUnLoad)
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [anyChangesForm])

    return (
      <Component
        {...props}
        anyChangesForm={anyChangesForm}
        setAnyChangesFrom={setAnyChangesFrom}
      />
    )
  }
  return WithDiscardRouteChange
}

export default withDiscardRouteChange
