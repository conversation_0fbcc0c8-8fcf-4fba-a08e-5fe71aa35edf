import { ReactElement, useEffect, useMemo, useState } from 'react'

import Cookies from 'js-cookie'
import { getUserAuthentication } from './auth-utilities'
import { SESSION_COOKIE_CURRENT_TENANT } from '~/core/constants/cookies'

const withClientSidePagePropsUser = <
  T extends { pageProps: object; showOnServer?: boolean }
>(
  Component: (props: T) => ReactElement
) => {
  const WithPagePropUser = <A extends T>(props: A) => {
    const [user, setUser] = useState<ReturnType<
      typeof getUserAuthentication
    > | null>()
    const cookies = Cookies.get() || {}
    useEffect(() => {
      setUser(getUserAuthentication(cookies) || null)
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cookies[SESSION_COOKIE_CURRENT_TENANT]])
    const pageProps = useMemo(
      () => ({ ...props.pageProps, user }),
      [user, props.pageProps]
    )
    return user !== undefined || props.showOnServer ? (
      <Component {...props} pageProps={pageProps} />
    ) : (
      // it must be an app skeleton without user infomation
      <></>
    )
  }
  return WithPagePropUser
}

export default withClientSidePagePropsUser
