import { useEffect, useState } from 'react'
import { IMember } from '../features/settings/members/types'
import useSwitchRole from 'src/hooks/authorization/use-switch-role'
import { ITeam } from '../features/settings/teams/types'
import { ISelectOption } from '~/core/ui/Select'

export const useMappingFilterData = () => {
  const switchRole = useSwitchRole()

  const mappingTeamsData = (collection: ITeam[], metadata: any) => {
    const cloneData = collection.map((item: ITeam) => {
      return {
        id: item.id,
        name: item.name,
        parentId: item.parentId,
        subordinates: item.subordinates
      }
    })

    const newCloneData = [] as Array<ISelectOption>
    cloneData.forEach((item: ITeam) => {
      newCloneData.push({
        value: String(item.id),
        parentId: undefined,
        supportingObj: {
          name: item.name || ''
        }
      })

      if (item.subordinates?.length) {
        item.subordinates.forEach(
          (sub: {
            id?: string
            name?: string
            parentId?: number
            membersCount?: number
          }) => {
            newCloneData.push({
              value: String(sub.id),
              parentId: String(item.id),
              supportingObj: {
                name: sub.name || ''
              }
            })
          }
        )
      }
    })

    return {
      metadata,
      collection: newCloneData
    }
  }

  const mappingMembersData = (collection: IMember[], metadata: any) => {
    const fullList = collection.map((member) => ({
      value: String(member.id),
      avatar: member.avatarVariants?.thumb?.url,
      avatarVariants: member.avatarVariants,
      supportingObj: {
        name: member.fullName,
        defaultColour: member.defaultColour
      }
    }))
    return {
      metadata,
      collection: switchRole({
        default: fullList,
        member: fullList
      })
    }
  }

  return {
    mappingMembersData,
    mappingTeamsData
  }
}
