import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useRouter } from 'next/router'

type Props = {
  message?: string
}

const defaultMessage =
  'Discard unsaved changes. Changes you’ve made will not be saved.'

function useLeaveConfirm({ message = '' }: Props = {}) {
  const Router = useRouter()
  const [anyChangesForm, setAnyChangesForm] = useState(false)

  const finalMSG = useMemo(() => message || defaultMessage, [message])
  const temp = useRef({ anyChangesForm: false, preventChange: false })
  temp.current.anyChangesForm = anyChangesForm
  const onRouteChangeStart = useCallback(() => {
    if (!temp.current.preventChange && temp.current.anyChangesForm) {
      // eslint-disable-next-line no-alert
      if (window.confirm(finalMSG)) {
        return true
      }
      // Router.replace(Router.asPath)
      // eslint-disable-next-line @typescript-eslint/no-throw-literal
      // throw "Abort route change by user's confirmation."
      // return false
      temp.current.preventChange = true

      history.go(1)
    } else {
      temp.current.preventChange = false
    }

    return null
  }, [anyChangesForm, finalMSG])

  useEffect(() => {
    Router.events.on('routeChangeStart', onRouteChangeStart)
    return () => {
      Router.events.off('routeChangeStart', onRouteChangeStart)
    }
  }, [])

  useEffect(() => {
    const handleBeforeUnload = () => {
      if (anyChangesForm) {
        return true
      }

      return undefined
    }

    if (typeof window !== 'undefined') {
      window.onbeforeunload = handleBeforeUnload
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.onbeforeunload = null
      }
    }
  }, [anyChangesForm])

  return { anyChangesForm, setAnyChangesForm }
}

export default useLeaveConfirm
