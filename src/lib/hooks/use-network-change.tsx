import { useEffect, useRef } from 'react'

export const useNetworkChange = (onChange: () => void) => {
  const previousConnectionType = useRef<string | null>(null)

  useEffect(() => {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection

    if (connection && connection.addEventListener) {
      previousConnectionType.current = connection.type
      connection.addEventListener('change', onChange)
    }

    window.addEventListener('online', onChange)
    window.addEventListener('offline', onChange)

    return () => {}
  }, [onChange])
}
