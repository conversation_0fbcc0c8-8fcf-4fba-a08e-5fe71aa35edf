import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import SocialLinks from '~/components/Candidates/SocialLinks'
import EditingInlineTableBody, {
  ENUMS_EDITING_INLINE_MODE
} from '~/components/EditingInlineTable/Body'
import EditingInlineTableHeader from '~/components/EditingInlineTable/Header'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import WithComputedMaxItemsChips from '~/components/WithComputedMaxItemsChips'
import configuration from '~/configuration'
import { ISelectOption } from '~/core/@types/global'
import { Avatar } from '~/core/ui/Avatar'
import { Checkbox } from '~/core/ui/Checkbox'
import Empty from '~/core/ui/Empty'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import { TableEditingPagination } from '~/core/ui/TableEditingPagination'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { convertValueToHTMLFromSearch } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'
import { pushStateBrowser } from '~/core/utilities/is-browser'
import { COMPANIES_DEFAULT_FILTER } from '~/features/agency/companies'
import { CompanyItemListingType } from '~/lib/features/agency/companies/types/company-detail'
import usePermissionCompany from '~/lib/features/permissions/hooks/use-permission-company'
import { IPagePaginationResult } from '~/lib/hooks/use-pagination-graph-page'
import useBoundStore from '~/lib/store'
import { CompaniesViewProps } from './CompaniesView'

// Types
interface CellInfo {
  row: { original: CompanyItemListingType }
  getValue: Function
}

export interface QueryKeyProps {
  search?: string
  page?: number
  key?: number
  parentIds?: ISelectOption[]
}

interface CompaniesListingTableProps extends CompaniesViewProps {
  tableRef?: (tableEditor: any) => void
  data?: IPagePaginationResult<CompanyItemListingType>
  refetch: () => void
  fetchPagination: (
    paginationParam?:
      | {
          pageSize: number
          currentPage: number
        }
      | undefined
  ) => void
  isFetching: boolean
  forceChangeCurrentPage: number
  sorting?: {
    [key: string]: string | null
  }
  setSorting?: (params: { [key: string]: string | null }) => void
}
// constants
const STICKY_COLUMNS = [
  {
    index: 0,
    position: 'left' as const,
    value: 0,
    useShadow: true,
    accessorKey: 'rowSelection'
  },
  {
    index: 1,
    position: 'left' as const,
    value: 40,
    useShadow: true,
    accessorKey: 'name'
  }
]

// Utility functions
const getColumnVisibility = (data: any) => {
  const permittedFields = data?.data?.[0]?.permittedFields

  return {
    rowSelection: true,
    name: !!permittedFields?.name,
    status: true,
    openJobsCount: true,
    archivedJobsCount: true,
    owner: !!permittedFields?.owner,
    location: !!permittedFields?.companyLocations,
    industries: !!permittedFields?.industries,
    domain: !!permittedFields?.domain,
    createdAt: true,
    updatedAt: true
  }
}

const CompaniesListingTable: FC<
  CompaniesViewProps & CompaniesListingTableProps
> = (props) => {
  const {
    tableRef: callbackTableRef,
    setOpenCreateCompany,
    filter,
    changeFilter,
    actions,

    sorting,
    setSorting,
    data,
    refetch,
    fetchPagination,
    forceChangeCurrentPage,
    isFetching
  } = props

  const { t } = useTranslation()
  const [showTable, setShowTable] = useState(true)
  const { setRefetchMyList, refetchMyList } = useBoundStore()
  const { actionCompany } = usePermissionCompany()
  const tableRef = useRef<any>(null)

  // Effects
  useEffect(() => {
    if (refetchMyList) {
      refetch()
      setRefetchMyList(false)
    }
  }, [refetchMyList, refetch, setRefetchMyList])

  useEffect(() => {
    const timer = setTimeout(() => {
      if (showTable === false) {
        setShowTable(true)
      }
    }, 5000)
    return () => clearTimeout(timer)
  }, [showTable])

  useEffect(() => {
    const prepareDataForSwitchLayout = () => {
      const mappingRedirectUrls = [] as Array<string>
      const collections = data?.data || []

      collections.forEach((s) => {
        const item = s
        if (item?.id) {
          mappingRedirectUrls.push(
            configuration.path.candidates.detail(item.id)
          )
        }
      })

      if (actions?.setConfigSwitchLayout) {
        actions?.setConfigSwitchLayout({
          path: actions?.configSwitchLayout.path,
          redirectUrls: mappingRedirectUrls
        })
      }
    }

    prepareDataForSwitchLayout()
  }, [data])

  useEffect(() => {
    if (tableRef.current && isFetching) {
      tableRef.current.toggleAllRowsSelected(false)
    }
  }, [isFetching])

  const handleViewDetails = useCallback(
    (id: string | undefined) => {
      if (!id) return
      if (actions?.setSwitchView) {
        actions.setSwitchView({ id, view: 'companies' })
        pushStateBrowser({
          state: { id },
          unused: '',
          url: configuration.path.agency.companyDetail(Number(id))
        })
      } else {
        window.open(
          configuration.path.agency.companyDetail(Number(id)),
          '_blank'
        )
      }
    },
    [actions]
  )

  const getCompanyActions = useCallback(
    (company: CompanyItemListingType) => {
      const baseActions = [
        {
          name: t('button:viewDetails'),
          iconMenus: 'ExternalLink' as const,
          onClick: async () => handleViewDetails(company?.id)
        }
      ]

      return baseActions
    },
    [handleViewDetails, t]
  )

  // Cell renderers
  const cellRenderers = useMemo(
    () => ({
      default: (value: string) => <TextCell content={value || '-'} />,
      name: (data: { id: string; name: string; url: string }) => (
        <div className="flex items-center">
          <div className="mr-1.5">
            <Avatar
              defaultAvatar={false}
              shape="rounded"
              src={data.url}
              alt={data.name}
              size="sm"
              color="#FFFFFF"
            />
          </div>
          <div className="line-clamp-1">
            <Tooltip content={data.name}>
              <TypographyText
                className="cursor-pointer truncate text-sm font-medium text-gray-900 hover:underline"
                onClick={() => handleViewDetails(data.id)}>
                {data.name}
              </TypographyText>
            </Tooltip>
          </div>
        </div>
      ),

      status: (data: string) => (
        <Tooltip
          classNameAsChild="truncate text-sm text-gray-900"
          content={data}>
          {data || '-'}
        </Tooltip>
      ),

      userAvatar: (user?: {
        id: string
        name: string
        url: string
        color: string
      }) => {
        if (!user) return <TextCell content="-" />
        return (
          <div className="flex items-center gap-1.5">
            <Tooltip content={user.name}>
              <Avatar
                size="sm"
                src={user.url}
                color={user.color}
                alt={user.name}
              />
            </Tooltip>
            <TextCell content={user.name} />
          </div>
        )
      },

      location: (data: { locations: any[]; label: string }) => {
        const { locations, label } = data
        return (
          <Tooltip
            position="bottom"
            content={
              <div
                dangerouslySetInnerHTML={{
                  __html: locations
                    .map((l) => `${l.state}, ${l.country}`)
                    .join('<br />')
                }}
              />
            }>
            <TypographyText className="line-clamp-1 text-sm text-gray-900">
              {label}
            </TypographyText>
          </Tooltip>
        )
      },

      industries: (data: { industries: any[] }) => {
        const { industries = [] } = data
        if (!industries.length) {
          return <TextCell content="-" />
        }
        return (
          <WithComputedMaxItemsChips
            totalCount={industries.length}
            className="relative -mt-2 flex w-[calc(100%_-_40px)] items-center">
            {({ maxItems }) => (
              <SuggestionInlineChips
                size="sm"
                source={industries.map((item) => ({
                  label: item?.name,
                  maxLength: 30
                }))}
                type="default"
                maxItems={maxItems}
              />
            )}
          </WithComputedMaxItemsChips>
        )
      },

      domain: (data: string) => {
        if (!data) {
          return <TextCell content="-" />
        }
        return <SocialLinks source={{ others: [data] }} maxItems={5} />
      }
    }),
    []
  )

  const getColumnSize = (
    type = 'system' as 'system' | 'id' | 'date' | 'text'
  ) => {
    const baseSize = configuration.tableColumnSize.mapping[type]
    const maxFreezeSize =
      configuration.tableColumnSize.maxFreeze -
      configuration.tableColumnSize.mapping.system -
      configuration.tableColumnSize.mapping.id

    return {
      size: baseSize,
      minSize: baseSize,
      maxSize: type === 'text' ? maxFreezeSize : baseSize * 2
    }
  }

  const createColumn = ({
    key,
    title,
    sortable = false,
    sizeType = 'text',
    getValue,
    cellRender
  }: {
    key: string
    title: string
    sortable?: boolean
    sizeType?: 'text' | 'id' | 'date' | 'system'
    getValue?: (info: CellInfo) => any
    cellRender: (defaultValue: {
      [key: string]: string | number
    }) => React.ReactNode
  }) => ({
    accessorKey: key,
    header: () => (
      <EditingInlineTableHeader
        headerConfig={{
          title,
          ...(sortable
            ? {
                sortingShow: true,
                sortingDescTitle: `${t(
                  `companiesListing:table:sorting:${key}:desc`
                )}`,
                sortingAscTitle: `${t(
                  `companiesListing:table:sorting:${key}:asc`
                )}`,
                sorting,
                setSorting
              }
            : {})
        }}
        accessorKey={key}
      />
    ),
    cell: (info: CellInfo) => (
      <EditingInlineTableBody
        bodyConfig={{
          mode: ENUMS_EDITING_INLINE_MODE.onlyView,
          content: cellRender(getValue?.(info) || '')
        }}
        accessorKey={key}
        {...(key === 'name' && {
          actions: getCompanyActions(info.row.original)
        })}
      />
    ),
    footer: (props: { column: { id: string } }) => props.column.id,
    ...getColumnSize(sizeType)
  })

  // Column definitions
  const COLUMN_DEFINITIONS = [
    {
      key: 'name',
      title: t('companiesListing:table:companyName'),
      sortable: true,
      getValue: (info: CellInfo) => ({
        id: info.row.original.id,
        name: info.row.original?.permittedFields?.name?.value,
        url: info.row.original.logoVariants?.thumb?.url
      }),
      cellRender: cellRenderers.name
    },
    {
      key: 'status',
      title: t('companiesListing:table:status'),
      getValue: (info: CellInfo) => info.row.original.companyStatus?.name,
      cellRender: cellRenderers.status
    },
    {
      key: 'openJobsCount',
      title: t('companiesListing:table:openJobs'),
      sortable: true,
      sizeType: 'id' as const,
      getValue: (info: CellInfo) =>
        info?.row?.original?.statistic?.openJobsCount,
      cellRender: cellRenderers.default
    },
    {
      key: 'archivedJobsCount',
      title: t('companiesListing:table:archivedJobs'),
      sizeType: 'id' as const,
      getValue: (info: CellInfo) =>
        info?.row?.original?.statistic?.archivedJobsCount,
      cellRender: cellRenderers.default
    },
    {
      key: 'owner',
      title: t('companiesListing:table:owner'),
      getValue: (info: CellInfo) => {
        const owner = info.row.original?.permittedFields?.owner?.value
        return owner
          ? {
              id: owner.id,
              name: owner.full_name,
              url: owner.avatar?.thumb?.url,
              color: owner.default_colour
            }
          : undefined
      },
      cellRender: cellRenderers.userAvatar
    },
    {
      key: 'location',
      title: t('companiesListing:table:location'),
      getValue: (info: CellInfo) => {
        const locations =
          info.row.original?.permittedFields?.companyLocations.value || []
        const label =
          locations.length > 1
            ? `${locations.length} ${t('label:locations')}`
            : locations.length === 1
            ? [locations[0].state, locations[0].country]
                .filter(Boolean)
                .join(', ')
            : '-'
        return {
          locations,
          label
        }
      },
      cellRender: cellRenderers.location
    },
    {
      key: 'industries',
      title: t('companiesListing:table:industry'),
      getValue: (info: CellInfo) =>
        info.row.original?.permittedFields?.industries?.value || [],
      cellRender: cellRenderers.industries
    },
    {
      key: 'domain',
      title: t('companiesListing:table:domain'),
      getValue: (info: CellInfo) =>
        info.row.original?.permittedFields?.domain?.value,
      cellRender: cellRenderers.domain
    },
    {
      key: 'createdAt',
      title: t('companiesListing:table:createAt'),
      sortable: true,
      sizeType: 'date' as const,
      getValue: (info: CellInfo) =>
        info?.row?.original?.createdAt
          ? defaultFormatDate(new Date(info?.row?.original?.createdAt))
          : '',
      cellRender: cellRenderers.default
    },
    {
      key: 'updatedAt',
      title: t('companiesListing:table:updateAt'),
      sortable: true,
      sizeType: 'date' as const,
      getValue: (info: CellInfo) =>
        info?.row?.original?.updatedAt
          ? defaultFormatDate(new Date(info.row.original.updatedAt))
          : '',
      cellRender: cellRenderers.default
    }
  ]

  const columns = useMemo(
    () => [
      {
        accessorKey: 'rowSelection',
        header: () => (
          <div className="flex items-center py-[7px]">
            <Checkbox
              isChecked={false}
              onCheckedChange={() => {}}
              size="sm"
              className="mr-1.5 mt-0 flex items-center pl-[1px]"
            />
          </div>
        ),
        cell: () => (
          <div className="checkedSystemId flex h-8 items-center pl-[1px]">
            <Checkbox
              isChecked={false}
              onCheckedChange={() => {}}
              size="sm"
              className="mr-1.5 mt-0 flex items-center"
            />
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        ...getColumnSize('system')
      },
      ...COLUMN_DEFINITIONS.map(createColumn)
    ],
    [COLUMN_DEFINITIONS, createColumn]
  )

  const topSpace = useClassBasedTopSpace({
    34: 'h-[calc(100vh_-_90px)]',
    default: 'h-[calc(100vh_-_56px)]'
  })

  // Empty state
  if (data?.meta.totalRowCount === 0 && filter?.isFilterTouched) {
    return (
      <div
        style={{ minHeight: 'calc(100vh - 170px)' }}
        className="flex flex-1 items-center">
        <Empty
          type="empty-search"
          title={t('companiesListing:table:emptySearch:title') || ''}
          description={
            t('companiesListing:table:emptySearch:description') || ''
          }
          buttonTitle={
            t('companiesListing:table:emptySearch:buttonClear') || ''
          }
          onClick={() => changeFilter(COMPANIES_DEFAULT_FILTER)}
        />
      </div>
    )
  }

  // Main table render
  return (
    <TableEditingPagination
      tableRef={(tableEditor: any) => {
        callbackTableRef && callbackTableRef(tableEditor)
        return (tableRef.current = tableEditor)
      }}
      search={{
        globalFilter: [
          ...(!!filter?.search ? [filter?.search] : []),
          ...(filter?.jobId?.value ? [filter?.jobId?.value] : []),
          ...(filter?.stageTypeId?.value ? [filter?.stageTypeId?.value] : [])
        ].join(' '),
        filter
      }}
      textOverride={{
        of: `${t('label:of')}`,
        page: `${t('label:page')}`,
        placeholder: `${t('label:placeholder:select')}`,
        search: `${t('label:placeholder:search')}`,
        loading: `${t('label:loading')}`,
        noOptions: `${t('label:noOptions')}`,
        rowsPerPage: `${t('label:rowsPerPage')}`
      }}
      emptyConfig={{
        classNameEmpty: cn('h-full items-center justify-center flex', topSpace),
        title: `${t('companiesListing:table:emptyData:title')}`,
        description: `${t('companiesListing:table:emptyData:description')}`,
        buttonTitle: actionCompany.create
          ? `${t('companiesListing:table:emptyData:buttonNewCompany')}`
          : '',
        buttonTitleOnClick: () => setOpenCreateCompany(true),
        titleSearch: `${t('companiesListing:table:emptySearch:title')}`,
        descriptionSearch: `${t(
          'companiesListing:table:emptySearch:description'
        )}`,
        buttonTitleSearch: `${t(
          'companiesListing:table:emptySearch:buttonClear'
        )}`,
        buttonTitleSearchOnClick: () => changeFilter({ page: 1 })
      }}
      tableConfig={{
        defaultPageSize: configuration.defaultPageSize,
        showRowsPerPage: true,
        paginationInsideScroll: true,
        heightOfColumn: 33
      }}
      dataQuery={{
        isFetching,
        fetcher: {
          fetchPagination,
          forceChangeCurrentPage
        },
        data
      }}
      columnVisibility={getColumnVisibility(data)}
      columns={columns}
      isHeaderSticky
      stickyConfig={STICKY_COLUMNS}
      classNameTable={cn(
        'max-w-full bg-white border-t-[1px] border-solid border-t-gray-100',
        // classNameTable,
        topSpace
      )}
    />
  )
}

export default CompaniesListingTable

// Custom component
const TextCell = ({ content }: { content: string | number }) => {
  return (
    <div className="flex items-center">
      <TypographyText className="text-sm text-gray-900">
        {content}
      </TypographyText>
    </div>
  )
}
