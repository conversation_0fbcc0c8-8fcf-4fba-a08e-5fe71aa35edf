const fs = require('fs')
const path = require('path')
const config = require('dotenv').config({
  path: path.resolve(__dirname, '../.env')
})

if (config.error) {
  console.log('.env not found use machine env')
}

// Should following the name of folder `locales`. Ex: en|jp|vn
const listLanguage = ['en', 'jp', 'vn']

const staticMap = {
  activity: { name: 'activity' },
  'agency-company-listing': { name: 'agency-company-listing' },
  'agency-company': { name: 'agency-company' },
  'agency-setting-company-status': { name: 'agency-setting-company-status' },
  'assign-job': { name: 'assign-job' },
  auth: { name: 'auth' },
  button: { name: 'button' },
  candidates: { name: 'candidates' },
  careers: { name: 'careers' },
  common: { name: 'common' },
  form: { name: 'form' },
  interview: { name: 'interview' },
  job: { name: 'job' },
  label: { name: 'label' },
  notification: { name: 'notification' },
  onboarding: { name: 'onboarding' },
  referrals: { name: 'referrals' },
  report: { name: 'report' },
  requisitions: { name: 'requisitions' },
  settings: { name: 'settings' },
  'talent-pool': { name: 'talent-pool' },
  task: { name: 'task' },
  tooltip: { name: 'tooltip' }
}
const staticList = Object.values(staticMap)

async function createFolderIFNeed(path) {
  try {
    await fs.promises.access(path)
    return true
  } catch {
    await fs.promises.mkdir(path, { recursive: true })
  }
}

async function checkExisted(path) {
  try {
    await fs.promises.access(path)
    return true
  } catch {
    return false
  }
}

function validateKey(params) {
  let sourceKey = { ...params.SOURCE }
  let status = 'KEY'

  if (params.KEY && !params.Column2) {
    status = 'KEY'
    Object.assign(sourceKey, {
      KEY: params.KEY,
      Column2: undefined,
      Column3: undefined,
      Column4: undefined,
      Column5: undefined,
      Column6: undefined
    })
  }

  if ((params.KEY && params.Column2) || (params.Column2 && !params.KEY)) {
    status = 'Column2'
    Object.assign(sourceKey, {
      KEY: params.KEY || sourceKey.KEY,
      Column2: params.Column2,
      Column3: undefined,
      Column4: undefined,
      Column5: undefined,
      Column6: undefined
    })
  }

  if (params.KEY && params.Column2 && params.Column3) {
    status = 'Column3'
    Object.assign(sourceKey, {
      KEY: params.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: undefined,
      Column5: undefined,
      Column6: undefined
    })
  }

  if (params.KEY && params.Column2 && params.Column3 && params.Column4) {
    status = 'Column4'
    Object.assign(sourceKey, {
      KEY: params.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: undefined,
      Column6: undefined
    })
  }

  if (
    params.KEY &&
    params.Column2 &&
    params.Column3 &&
    params.Column4 &&
    params.Column5
  ) {
    status = 'Column5'
    Object.assign(sourceKey, {
      KEY: params.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: undefined
    })
  }

  if (
    params.KEY &&
    params.Column2 &&
    params.Column3 &&
    params.Column4 &&
    params.Column5 &&
    params.Column6
  ) {
    status = 'Column6'
    Object.assign(sourceKey, {
      KEY: params.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: params.Column6
    })
  }

  if (!params.KEY && (params.Column3 || (params.Column3 && params.Column2))) {
    status = 'Column3'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: params.Column2 || sourceKey.Column2,
      Column3: params.Column3,
      Column4: undefined,
      Column5: undefined,
      Column6: undefined
    })
  }

  if (!params.KEY && params.Column2 && params.Column3 && params.Column4) {
    status = 'Column4'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: undefined,
      Column6: undefined
    })
  }

  if (
    !params.KEY &&
    params.Column2 &&
    params.Column3 &&
    params.Column4 &&
    params.Column5
  ) {
    status = 'Column5'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: undefined
    })
  }

  if (
    !params.KEY &&
    params.Column2 &&
    params.Column3 &&
    params.Column4 &&
    params.Column5 &&
    params.Column6
  ) {
    status = 'Column6'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: params.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: params.Column6
    })
  }

  if (!params.KEY && (params.Column4 || (params.Column4 && params.Column3))) {
    status = 'Column4'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: sourceKey.Column2,
      Column3: params.Column3 || sourceKey.Column3,
      Column4: params.Column4,
      Column5: undefined,
      Column6: undefined
    })
  }

  if (!params.KEY && params.Column3 && params.Column4 && params.Column5) {
    status = 'Column5'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: sourceKey.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: undefined
    })
  }

  if (
    !params.KEY &&
    params.Column3 &&
    params.Column4 &&
    params.Column5 &&
    params.Column6
  ) {
    status = 'Column6'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: sourceKey.Column2,
      Column3: params.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: params.Column6
    })
  }

  if (!params.KEY && (params.Column5 || (params.Column5 && params.Column4))) {
    status = 'Column5'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: sourceKey.Column2,
      Column3: sourceKey.Column3,
      Column4: params.Column4 || sourceKey.Column4,
      Column5: params.Column5,
      Column6: undefined
    })
  }

  if (!params.KEY && (params.Column6 || (params.Column6 && params.Column5))) {
    status = 'Column6'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: sourceKey.Column2,
      Column3: sourceKey.Column3,
      Column4: sourceKey.Column4,
      Column5: params.Column5 || sourceKey.Column5,
      Column6: params.Column6
    })
  }

  if (!params.KEY && params.Column4 && params.Column5 && params.Column6) {
    status = 'Column5'
    Object.assign(sourceKey, {
      KEY: sourceKey.KEY,
      Column2: sourceKey.Column2,
      Column3: sourceKey.Column3,
      Column4: params.Column4,
      Column5: params.Column5,
      Column6: params.Column6
    })
  }

  return {
    status,
    sourceKey
  }
}

createFolderIFNeed(path.resolve(__dirname, '../src/public/locales')).then(
  () => {
    const promiseList = staticList.map(async (item) => {
      const translationFilePath = path.resolve(
        __dirname,
        '../excel-convert/i18n.json'
      )
      const translationExited = await checkExisted(translationFilePath)

      if (translationExited) {
        const readFile = await fs.promises.readFile(translationFilePath)
        var arrayFile = readFile.toString().split('\n')
        const object = JSON.parse(arrayFile.join(''))
        const sourceName = object[item.name]

        const jsonFilePath = path.resolve(
          __dirname,
          `../public/locales/en/${item.name}.json`
        )
        const jsonExited = await checkExisted(jsonFilePath)

        if (jsonExited) {
          for (let i = 0; i < listLanguage.length; i++) {
            const filePath = path.resolve(
              __dirname,
              `../public/locales/${
                listLanguage[i] === 'jp' ? 'ja' : listLanguage[i]
              }/${item.name}.json`
            )
            const sourcePath = path.resolve(
              __dirname,
              `../public/locales/en/${item.name}.json`
            )

            const readFile = await fs.promises.readFile(sourcePath)
            var arrayFile = readFile.toString().split('\n')
            const object = JSON.parse(arrayFile.join(''))
            let data = { ...object }
            let SOURCE = {
              KEY: undefined,
              Column2: undefined,
              Column3: undefined,
              Column4: undefined,
              Column5: undefined,
              Column6: undefined
            }

            for (let z = 0; z < sourceName.length; z++) {
              const { status, sourceKey } = validateKey({
                SOURCE,
                KEY: sourceName[z].KEY,
                Column2: sourceName[z].Column2,
                Column3: sourceName[z].Column3,
                Column4: sourceName[z].Column4,
                Column5: sourceName[z].Column5,
                Column6: sourceName[z].Column6
              })
              const value =
                sourceName[z][listLanguage[i].toUpperCase()] ||
                sourceName[z]['EN'] ||
                ''
              SOURCE = sourceKey

              switch (status) {
                case 'Column2':
                  data[sourceKey.KEY][sourceKey.Column2] = value
                  break

                case 'Column3':
                  data[sourceKey.KEY][sourceKey.Column2][sourceKey.Column3] =
                    value
                  break

                case 'Column4':
                  data[sourceKey.KEY][sourceKey.Column2][sourceKey.Column3][
                    sourceKey.Column4
                  ] = value

                case 'Column5':
                  data[sourceKey.KEY][sourceKey.Column2][sourceKey.Column3][
                    sourceKey.Column4
                  ][sourceKey.Column5] = value
                  break

                case 'Column6':
                  data[sourceKey.KEY][sourceKey.Column2][sourceKey.Column3][
                    sourceKey.Column4
                  ][sourceKey.Column5][sourceKey.Column6] = value
                  break

                default:
                  data[sourceKey.KEY] = value
                  break
              }
            }

            try {
              await fs.promises
                .writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')
                .then(() => filePath)
            } catch (e) {
              console.error(e)
            }
          }
        }
      }
    })

    return Promise.all(promiseList)
  }
)
